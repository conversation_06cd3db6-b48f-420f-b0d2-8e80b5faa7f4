import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Text } from '@/components/ui/text';
import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';

export interface SelectionOption {
  id: string;
  label: string;
  disabled?: boolean;
}

interface SelectionGroupProps {
  label?: string;
  hint?: string;
  options: SelectionOption[];
  value: string | string[];
  onChange: (value: string | string[]) => void;
  multiple?: boolean;
  horizontal?: boolean;
  error?: string;
  disabled?: boolean;
  buttonSize?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'secondary' | 'destructive' | 'ghost' | 'link';
  selectedVariant?: 'default' | 'secondary' | 'destructive' | 'ghost' | 'link';
  extraChildren?: React.ReactNode;
}

export function SelectionGroup({
  label,
  hint,
  options,
  value,
  onChange,
  multiple = false,
  horizontal = true,
  error,
  disabled = false,
  buttonSize = 'sm',
  variant = 'secondary',
  selectedVariant = 'default',
  extraChildren,
}: SelectionGroupProps) {
  const handleOptionToggle = (optionId: string) => {
    if (disabled) return;

    if (multiple) {
      // 多选模式
      const currentValues = Array.isArray(value) ? value : [];
      
      if (currentValues.includes(optionId)) {
        // 移除选项
        const newValues = currentValues.filter(id => id !== optionId);
        onChange(newValues);
      } else {
        // 添加选项
        onChange([...currentValues, optionId]);
      }
    } else {
      // 单选模式
      const currentValue = Array.isArray(value) ? value[0] : value;
      
      if (currentValue === optionId) {
        // 如果点击已选中的选项，可以取消选择（可选行为）
        onChange('');
      } else {
        // 选择新选项
        onChange(optionId);
      }
    }
  };

  const isOptionSelected = (optionId: string): boolean => {
    if (multiple) {
      const currentValues = Array.isArray(value) ? value : [];
      return currentValues.includes(optionId);
    } else {
      const currentValue = Array.isArray(value) ? value[0] : value;
      return currentValue === optionId;
    }
  };

  const renderButtons = () => {
    const buttonElements = options.map((option) => {
      const isSelected = isOptionSelected(option.id);
      const isOptionDisabled = disabled || option.disabled;

      return (
        <Button
          key={option.id}
          variant={isSelected ? selectedVariant : variant}
          size={buttonSize}
          onPress={() => handleOptionToggle(option.id)}
          disabled={isOptionDisabled}
          style={horizontal ? styles.horizontalButton : styles.verticalButton}
        >
          <Text>{option.label}</Text>
        </Button>
      );
    });

    if (extraChildren) {
      return (
        <View style={horizontal ? styles.horizontalContainer : styles.verticalContainer}>
          <View style={horizontal ? styles.horizontalButtons : styles.verticalButtons}>
            {buttonElements}
          </View>
          <View style={styles.extraChildrenContainer}>
            {extraChildren}
          </View>
        </View>
      );
    }

    return (
      <View style={horizontal ? styles.horizontalButtons : styles.verticalButtons}>
        {buttonElements}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {label && <Label>{label}</Label>}
      {hint && <Text style={styles.hintText}>{hint}</Text>}
      
      {horizontal ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={[styles.scrollView]}
        >
          {renderButtons()}
        </ScrollView>
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={[styles.scrollView]}
        >
          {renderButtons()}
        </ScrollView>
      )}
      
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 8,
  },
  hintText: {
    color: '#666',
    fontSize: 12,
    marginBottom: 8,
  },
  scrollView: {
    flexGrow:0
  },
  horizontalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  verticalContainer: {
    gap: 8,
  },
  horizontalButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  verticalButtons: {
    gap: 8,
    paddingVertical: 4,
  },
  horizontalButton: {
  },
  verticalButton: {
    width: '100%',
  },
  extraChildrenContainer: {
    marginLeft: 0,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
  },
});
