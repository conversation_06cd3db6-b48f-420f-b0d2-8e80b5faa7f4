import { useColorScheme as useNativeColorScheme } from 'react-native';
import { useAppStore } from '@/lib/store';

export function useColorScheme() {
  const systemColorScheme = useNativeColorScheme();
  const { settings } = useAppStore();

  // 根据设置返回实际的颜色方案
  if (settings.theme === 'system') {
    return systemColorScheme;
  }

  return settings.theme;
}

// 导出原生的hook以备需要
export { useNativeColorScheme };
