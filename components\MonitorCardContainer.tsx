import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { Config, MonitoringStatus, SUIConfig, ThreeXUIConfig, XUIConfig } from '@/lib/types';
import SimpleThreeXUIMonitorCard from '@/panels/3x-ui/components/SimpleMonitorCard';
import SimpleSUIMonitorCard from '@/panels/s-ui/components/SimpleMonitorCard';
import SimpleXUIMonitorCard from '@/panels/x-ui/components/SimpleMonitorCard';
import { BottomSheetBackdrop, BottomSheetModal, BottomSheetView, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Alert, AppState, StyleSheet, View } from 'react-native';
import { Button } from './ui/button';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';
import { Label } from './ui/label';
import { Badge } from './ui/badge';

// 导入各面板的工具函数
import { getThreeXUIServerStatus, getThreeXUIDatabase, importThreeXUIDatabase } from '@/panels/3x-ui/utils';
import { getXUIServerStatus } from '@/panels/x-ui/utils';
import { getSUIServerStatus } from '@/panels/s-ui/utils';
import { useFocusEffect } from '@react-navigation/native';

interface MonitorCardContainerProps {
  configs: Config[];
}

export default function MonitorCardContainer({ configs }: MonitorCardContainerProps) {
  const { t } = useTranslation();
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const {
    deleteConfig,
    setMonitoringStatus,
    getProxyServer,
    persistMonitoringStatus
  } = useAppStore();

  // 轮询状态
  const intervalRef = useRef<number | null>(null);
  const lastUpdateTimeRef = useRef<Record<string, number>>({});

  // BottomSheet 状态
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const importModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ["20%"], []);
  const importSnapPoints = useMemo(() => ["60%"], []);
  const [selectedConfig, setSelectedConfig] = useState<Config | null>(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedImportConfigId, setSelectedImportConfigId] = useState<string>('');

  // 修复：避免 setInterval 回调捕获旧的 configs，使用 ref 始终读取最新的配置
  const configsRef = useRef<Config[]>(configs);
  useEffect(() => {
    configsRef.current = configs;
    // 如果正在轮询，配置变化时立即拉取一次最新状态，覆盖新加入的卡片
    if (intervalRef.current) {
      fetchAllServerStatus();
    }
  }, [configs]);



  // 获取单个配置的服务器状态
  const fetchServerStatus = async (config: Config) => {
    const requestTime = Date.now();

    try {
      let serverStatus = null;
      switch (config.type) {
        case '3x-ui':
          serverStatus = await getThreeXUIServerStatus(config as ThreeXUIConfig);
          break;
        case 'x-ui':
          serverStatus = await getXUIServerStatus(config as XUIConfig);
          break;
        case 's-ui':
          serverStatus = await getSUIServerStatus(config as SUIConfig);
          break;
      }

      if (!serverStatus) {
        throw new Error('Failed to get server status');
      }

      // 检查是否是最新的请求响应
      if (requestTime < (lastUpdateTimeRef.current[config.id] || 0)) {
        console.log('Ignoring outdated response for config:', config.id);
        return;
      }

      // 更新最后更新时间
      lastUpdateTimeRef.current[config.id] = requestTime;

      const newStatus: MonitoringStatus = {
        isOnline: true,
        lastUpdate: new Date(requestTime).toISOString(),
        failureCount: 0,
        serverStatus,
      };

      setMonitoringStatus(config.id, newStatus);
    } catch (error) {
      console.error('Failed to fetch server status for config:', config.id, error);

      // 检查当前时间是否大于最后更新时间，如果是则设为离线
      const lastUpdateTime = lastUpdateTimeRef.current[config.id] || 0;
      if (requestTime > lastUpdateTime) {
        // 更新最后更新时间
        lastUpdateTimeRef.current[config.id] = requestTime;

        const offlineStatus: MonitoringStatus = {
          isOnline: false,
          lastUpdate: new Date(requestTime).toISOString(),
          failureCount: 1,
        };
        setMonitoringStatus(config.id, offlineStatus);
      }
    }
  };

  // 获取所有配置的服务器状态
  const fetchAllServerStatus = async () => {
    // TODO: 如果设置了中转服务器，使用中转服务器获取状态
    const proxyServer = getProxyServer();
    if (proxyServer) {
      // 中转服务器逻辑暂不实现
      console.log('Proxy server configured but not implemented yet');
      return;
    }
    // 直接轮询各个服务器
    const currentConfigs = configsRef.current || [];
    const promises = currentConfigs.map(config => fetchServerStatus(config));
    await Promise.allSettled(promises);
  };

  // 开始轮询
  const startPolling = () => {
    // 如果已经在轮询，直接返回
    if (intervalRef.current) return;

    // 先设置轮询间隔，避免并发调用时出现重复计时器
    intervalRef.current = setInterval(() => {
      fetchAllServerStatus();
    }, 2500);
  };

  // 停止轮询
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // 使用 useFocusEffect 管理轮询
  useFocusEffect(
    useCallback(() => {
      // 当组件获得焦点时开始轮询
      startPolling();

      return () => {
        // 当组件失去焦点时停止轮询并持久化监控状态
        stopPolling();
        persistMonitoringStatus();
      };
    }, [])
  );

  // 监听应用状态变化
  useEffect(() => {
    const handleAppStateChange = async (nextAppState: string) => {
      if (nextAppState !== 'active') {
        // 应用进入后台或非活跃状态时，手动持久化监控状态
        await persistMonitoringStatus();
        // 停止轮询
        stopPolling();
      } else {
        // 应用回到前台时，如果没有在轮询，重新开始轮询
        if (!intervalRef.current) {
          startPolling();
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [persistMonitoringStatus]);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 获取主机名
  const getHostname = (config: Config): string => {
    try {
      // 如果URL已经包含协议，直接使用
      if (config.url.startsWith('http://') || config.url.startsWith('https://')) {
        const url = new URL(config.url);
        return url.hostname;
      } else {
        // 如果URL不包含协议，添加协议后解析
        const url = new URL(`${config.protocol}://${config.url}`);
        return url.hostname;
      }
    } catch {
      // 如果解析失败，返回原始URL
      return config.url;
    }
  };



  // 处理卡片点击
  const handleCardPress = useCallback((config: Config) => {
    router.replace(`/${config.type === 'x-ui' ? '3x-ui' : config.type}/${config.id}` as any);
  }, []);

  // 处理卡片长按
  const handleCardLongPress = useCallback((config: Config) => {
    setSelectedConfig(config);
    bottomSheetModalRef.current?.present();
  }, []);

  // 处理编辑配置
  const handleEdit = () => {
    if (!selectedConfig) return;

    bottomSheetModalRef.current?.dismiss();

    // 使用 router.push 导航到根级别的配置表单模态框
    router.push({
      pathname: `/config-form/${selectedConfig.type}`,
      params: {
        configId: selectedConfig.id,
      },
    });
  }

  // 处理导入配置
  const handleImport = useCallback(() => {
    if (!selectedConfig) return;

    // 检查是否支持导入
    if (selectedConfig.type !== '3x-ui') {
      Alert.alert(t('common.error'), t('common.unsupportedType'));
      return;
    }

    bottomSheetModalRef.current?.dismiss();
    setSelectedImportConfigId(''); // 重置选中的配置
    setShowImportModal(true);
    importModalRef.current?.present();
  }, [selectedConfig, t]);

  // 处理从指定配置导入
  const handleImportFromConfig = useCallback(async () => {
    if (!selectedConfig || !selectedImportConfigId) {
      return;
    }

    const sourceConfig = configs.find(c => c.id === selectedImportConfigId);
    if (!sourceConfig || selectedConfig.type !== '3x-ui' || sourceConfig.type !== '3x-ui') {
      Alert.alert(t('common.error'), t('common.unsupportedType'));
      return;
    }

    try {
      // 从源配置获取数据库
      const dbData = await getThreeXUIDatabase(sourceConfig as ThreeXUIConfig);
      if (!dbData) {
        Alert.alert(t('common.error'), '获取源配置数据失败');
        return;
      }

      // 导入到目标配置
      const success = await importThreeXUIDatabase(selectedConfig as ThreeXUIConfig, dbData);
      if (success) {
        Alert.alert(t('common.success'), t('common.importSuccess'));
      } else {
        Alert.alert(t('common.error'), t('common.importError'));
      }
    } catch (error) {
      console.error('Import config failed:', error);
      Alert.alert(t('common.error'), t('common.importError'));
    } finally {
      importModalRef.current?.dismiss();
      setShowImportModal(false);
      setSelectedImportConfigId('');
    }
  }, [selectedConfig, selectedImportConfigId, configs, t]);

  // 渲染导入底部弹窗footer
  const renderImportFooter = useCallback(
    (props: any) => (
      <View style={[styles.bottomSheetFooter, { backgroundColor, borderTopColor: borderColor }]}>
        <Button
          onPress={handleImportFromConfig}
          disabled={!selectedImportConfigId}
          style={[styles.importButton, { opacity: !selectedImportConfigId ? 0.5 : 1 }]}
        >
          <Text style={styles.importButtonText}>
            {t('common.import')}
          </Text>
        </Button>
      </View>
    ),
    [selectedImportConfigId, backgroundColor, borderColor, handleImportFromConfig, t]
  );

  // 处理删除配置
  const handleDelete = useCallback(() => {
    if (!selectedConfig) return;

    bottomSheetModalRef.current?.dismiss();
    Alert.alert(
      t('common.confirm'),
      t('common.deleteConfirmMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteConfig(selectedConfig.id);
            } catch (error) {
              console.error('Failed to delete config:', error);
              Alert.alert(t('common.error'), t('common.deleteError'));
            }
          },
        },
      ]
    );
  }, [selectedConfig, t, deleteConfig]);

  const renderMonitorCard = (config: Config) => {
    switch (config.type) {
      case 's-ui':
        return <SimpleSUIMonitorCard config={config as SUIConfig} onPress={handleCardPress} onLongPress={handleCardLongPress} />;
      case 'x-ui':
        return <SimpleXUIMonitorCard config={config as XUIConfig} onPress={handleCardPress} onLongPress={handleCardLongPress} />;
      case '3x-ui':
        return <SimpleThreeXUIMonitorCard config={config as ThreeXUIConfig} onPress={handleCardPress} onLongPress={handleCardLongPress} />;
      default:
        return null;
    }
  };

  return (
    <>
      {configs.map((config) => (
        <React.Fragment key={config.id}>
          {renderMonitorCard(config)}
        </React.Fragment>
      ))}
      
      {/* BottomSheet */}
      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[styles.bottomSheetContent, { backgroundColor }]}>

          <View style={styles.buttonsContainer}>
            <Button
              variant='ghost'
              onPress={handleEdit}
              size='lg'
            >
              <Text style={[styles.buttonText, { color: textColor }]}>
                {t('common.edit')}
              </Text>
            </Button>

            <View style={[styles.divider, { backgroundColor: borderColor }]} />

            <Button
              variant='ghost'
              onPress={handleImport}
              disabled={selectedConfig?.type !== '3x-ui'}
              size='lg'
            >
              <Text style={[
                styles.buttonText,
                { color: selectedConfig?.type === '3x-ui' ? textColor : '#999' }
              ]}>
                {t('common.importConfig')}
              </Text>
            </Button>

            <View style={[styles.divider, { backgroundColor: borderColor }]} />

            <Button
              variant='ghost'
              onPress={handleDelete}
              size='lg'
            >
              <Text style={[styles.buttonText, styles.deleteButtonText]}>
                {t('common.delete')}
              </Text>
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheetModal>

      {/* 导入配置模态框 */}
      <BottomSheetModal
        ref={importModalRef}
        index={1}
        snapPoints={importSnapPoints}
        backdropComponent={renderBackdrop}
        footerComponent={renderImportFooter}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetScrollView style={[styles.bottomSheetContent, { backgroundColor }]}>
          <Text style={[styles.modalTitle, { color: textColor }]}>
            {t('common.selectConfigToImport')}
          </Text>

          <View style={styles.configList}>
            {configs.filter(config =>
              config.type === '3x-ui' &&
              config.id !== selectedConfig?.id
            ).length === 0 ? (
              <Text style={[styles.emptyText, { color: '#999' }]}>
                没有可用的3X-UI配置
              </Text>
            ) : (
              <RadioGroup
                value={selectedImportConfigId}
                onValueChange={setSelectedImportConfigId}
              >
                {configs
                  .filter(config =>
                    config.type === '3x-ui' &&
                    config.id !== selectedConfig?.id
                  )
                  .map((config) => (
                    <View key={config.id} style={styles.radioItem}>
                      <RadioGroupItem value={config.id} />
                      <View style={styles.configInfo}>
                        <View style={styles.titleRow}>
                          <Label style={[styles.configName, { color: textColor }]}>
                            {config.name}
                          </Label>
                          <Badge variant="secondary" style={styles.hostBadge}>
                            <Text style={styles.hostBadgeText}>{getHostname(config)}</Text>
                          </Badge>
                        </View>
                      </View>
                    </View>
                  ))}
              </RadioGroup>
            )}
          </View>
        </BottomSheetScrollView>
      </BottomSheetModal>
    </>
  );
}

const styles = StyleSheet.create({
  bottomSheetContent: {
    padding: 0,
  },
  buttonsContainer: {
    flex: 1,
    justifyContent: 'space-around',
  },
  divider: {
    height: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  deleteButtonText: {
    color: '#ef4444',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  configList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  configItem: {
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    alignItems: 'flex-start',
    padding: 12,
  },
  configName: {
    fontSize: 16,
    fontWeight: '500',
  },
  configUrl: {
    fontSize: 14,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 20,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  configInfo: {
    flex: 1,
    marginLeft: 12,
  },
  importButton: {
    width: '100%',
  },
  importButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  bottomSheetFooter: {
    padding: 16,
    borderTopWidth: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  hostBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  hostBadgeText: {
    fontSize: 11,
  },
});
