import { Label } from '@/components/ui/label';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Stack, router } from 'expo-router';
import React from 'react';

export default function ThreeXUILayout() {

  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
       {/* 路由规则配置模态框 */}
      <Stack.Screen
        name="(modal)"
        options={{
          presentation:'modal',
        }}
      />
    </Stack>
  );
}
