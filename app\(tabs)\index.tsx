import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { getGroupDisplayName } from '@/lib/groupUtils';
import { useAppStore } from '@/lib/store';
import { router } from 'expo-router';
import { Plus } from 'lucide-react-native';
import React from 'react'
import { Alert, ScrollView, StyleSheet, View } from 'react-native';
import MonitorCardContainer from '~/components/MonitorCardContainer';
import { SelectionGroup, SelectionOption } from '~/components/selection-group';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function HomeScreen() {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const backgroundColor = useThemeColor({}, 'background');
  const insets = useSafeAreaInsets();

  // 根据主题设置颜色
  const iconColor = colorScheme === 'dark' ? '#ffffff' : '#000000';

  const { groups, selectedGroupId, setSelectedGroup, getFilteredConfigs, clearAllData } = useAppStore();



  const configs = getFilteredConfigs();

  // 按order排序分组
  const sortedGroups = [...groups].sort((a, b) => a.order - b.order);

  // 准备分组选项数据
  const groupOptions: SelectionOption[] = sortedGroups.map((group) => ({
    id: group.id,
    label: getGroupDisplayName(group, t),
  }));

  const handleAddConfig = () => {
    router.push('/add-config');
  };

  const handleGroupSelect = (groupId: string | string[]) => {
    // SelectionGroup返回的是string或string[]，我们只需要string
    const selectedId = Array.isArray(groupId) ? groupId[0] : groupId;
    setSelectedGroup(selectedId);
  };

  const handleEditGroups = () => {
    router.push('/edit-groups');
  };

  return (
    <View style={[styles.container, { backgroundColor,paddingTop: insets.top }]}>
      {/* 顶部导航栏 - 删除底部横线 */}
      <View style={styles.header}>
        <Text className="text-2xl font-bold">{t('home.title')}</Text>
        <View style={styles.headerButtons}>
          <Button
            variant="ghost"
            size="icon"
            onPress={handleAddConfig}
          >
            <Plus strokeWidth={3} size={25} color={iconColor} />
          </Button>
        </View>
      </View>

      {/* 分组选择器 */}
      <View style={styles.groupSection}>
        <SelectionGroup
          options={groupOptions}
          value={selectedGroupId || ''}
          onChange={handleGroupSelect}
          multiple={false}
          horizontal={true}
          buttonSize="sm"
          variant="secondary"
          selectedVariant="default"
          extraChildren={
            <View style={styles.extraButtons}>
              {/* 编辑按钮 */}
              <Button
                variant="secondary"
                size="sm"
                onPress={handleEditGroups}
              >
                <Text>{t('groups.editGroups')}</Text>
              </Button>
            </View>
          }
        />
      </View>

      {/* 配置列表区域 */}
      <ScrollView showsVerticalScrollIndicator={false} style={styles.configList}>
        {configs.length === 0 ? (
          <View style={styles.emptyState}>
            <Text className="text-lg text-muted-foreground text-center">
              {t('home.noConfigurations')}
            </Text>
            <Text className="text-sm text-muted-foreground text-center mt-2">
              {t('home.addFirstConfig')}
            </Text>
            <Button
              variant="default"
              onPress={handleAddConfig}
              style={styles.addFirstButton}
            >
              <Text>{t('home.addConfiguration')}</Text>
            </Button>
          </View>
        ) : (
          <View style={styles.configGrid}>
            <MonitorCardContainer configs={configs} />
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },

  groupSection: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    // 删除底部横线
    // borderBottomWidth: 1,
    // borderBottomColor: '#e5e5e5',
  },
  extraButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8, // 与选项组件间距保持一致
  },

  configList: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical:4
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  addFirstButton: {
    marginTop: 16,
  },
  configGrid: {
    gap: 12,
    paddingBottom:12
  },

});
