import { StyleSheet } from 'react-native';

// 监控卡片的统一样式
export const monitorCardStyles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    paddingVertical:8
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  titleContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  ipBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  ipBadgeText: {
    fontSize: 11,
  },
  subtitle: {
    fontSize: 14,
  },
  statusBadge: {
    marginLeft: 8,
  },
  badgeText: {
    fontSize: 12,
  },
  chartsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  chartItem: {
    alignItems: 'center',
  },
  chartBottomLabel: {
    marginTop:4,
    fontSize: 9,
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: 12,
  },
  pieContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  chartInnerContent: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartInnerLabel: {
    fontSize: 9,
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: 16,
  },
  percentageText: {
    fontSize: 11,
    fontWeight: '700',
    textAlign: 'center',
    lineHeight: 16,
    marginTop: 1,
  },
  networkStats: {
    marginTop: 4,
  },
  networkTrafficRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  networkTrafficItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
  },
  networkTrafficText: {
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
  networkIcon: {
    marginRight: 12,
  }
});

// Skeleton 专用样式
export const skeletonStyles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleSkeleton: {
    width: 120,
    height: 20,
    borderRadius: 4,
    marginRight: 8,
  },
  ipSkeleton: {
    width: 80,
    height: 20,
    borderRadius: 12,
  },
  statusSkeleton: {
    width: 60,
    height: 24,
    borderRadius: 12,
  },
  chartsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  chartItem: {
    alignItems: 'center',
  },
  chartCircleSkeleton: {
    width: 90,
    height: 90,
    borderRadius: 45,
    marginBottom: 8,
  },
  chartLabelSkeleton: {
    height: 16,
    borderRadius: 4,
  },
  chartLabel1: {
    width: 60,
  },
  chartLabel2: {
    width: 80,
  },
  chartLabel3: {
    width: 70,
  },
  networkStats: {
    marginTop: 8,
  },
  networkRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  networkItemSkeleton: {
    width: 120,
    height: 16,
    borderRadius: 4,
  },
});

// 根据使用率获取颜色的统一函数
export const getUsageColor = (percentage: number): string => {
  if (percentage < 60) {
    return 'green'; // 绿色
  } else if (percentage < 80) {
    return 'orange'; // 警告色（橙色）
  } else {
    return 'red'; // 红色
  }
};
