# Expo IAP 集成指南

本项目已经完成了 expo-iap 的集成，包括真实的订阅管理功能。

## ✅ 已完成的集成

### 1. 依赖安装
- ✅ expo-iap 已安装 (v2.4.4)
- ✅ expo-build-properties 已配置

### 2. 配置文件
- ✅ app.json 已配置 Android Kotlin 2.0.21
- ✅ expo-iap 插件已添加

### 3. Hook 实现
- ✅ useSubscription Hook 已更新为真实的 IAP 实现
- ✅ 支持购买、恢复购买、错误处理
- ✅ 自动状态同步和持久化

### 4. UI 集成
- ✅ Settings 页面已集成订阅功能
- ✅ 连接状态显示
- ✅ 错误信息显示
- ✅ 购买状态指示器
- ✅ 开发环境调试组件

## 🚀 如何使用

### 基本用法

```typescript
import { useSubscription } from '@/hooks/useSubscription';

function MyComponent() {
  const {
    isPro,           // 是否为Pro用户
    isLoading,       // 是否正在加载
    isConnected,     // 是否连接到应用商店
    purchasePro,     // 购买Pro订阅
    restorePurchases,// 恢复购买
    error,           // 错误信息
    subscriptions,   // 可用订阅产品
    currentPurchase  // 当前购买状态
  } = useSubscription();

  return (
    <View>
      <Text>Pro Status: {isPro ? 'Active' : 'Inactive'}</Text>
      <Button
        onPress={purchasePro}
        disabled={isLoading || !isConnected}
      >
        Purchase Pro
      </Button>
    </View>
  );
}
```

## 📋 下一步：配置应用商店产品

### iOS (App Store Connect)
1. 登录 App Store Connect
2. 选择你的应用
3. 进入 "功能" > "App 内购买项目"
4. 创建新的订阅产品
5. 设置产品ID为 `pro_subscription`（或更新代码中的 PRODUCT_ID）

### Android (Google Play Console)
1. 登录 Google Play Console
2. 选择你的应用
3. 进入 "货币化" > "产品" > "订阅"
4. 创建新的订阅产品
5. 设置产品ID为 `pro_subscription`

## 🧪 测试指南

### 开发环境测试
- 在开发环境下，Settings页面会显示调试信息
- 可以查看连接状态、产品数量、错误信息等

### iOS 测试
- 使用 Xcode 的 StoreKit Configuration 文件进行本地测试
- 使用 TestFlight 进行沙盒测试

### Android 测试
- 使用 Google Play Console 的内部测试轨道
- 确保测试账户已添加到许可测试人员列表

## 🔧 高级配置

### 自定义产品ID
在 `hooks/useSubscription.ts` 中修改：
```typescript
const PRODUCT_ID = 'your_custom_product_id';
```

### 错误处理自定义
Hook 已经包含了完整的错误处理，支持：
- 用户取消购买
- 网络错误
- 产品不可用
- 已拥有产品
- 其他未知错误

### 购买成功回调
Hook 会自动：
- 更新 Pro 状态
- 保存到 SecureStore
- 完成交易
- 清除错误状态

## ⚠️ 重要注意事项

1. **测试环境**：始终在沙盒环境中测试，避免真实扣费
2. **收据验证**：生产环境建议添加服务器端验证
3. **错误处理**：Hook 已处理常见错误，UI 会显示友好提示
4. **用户体验**：按钮会根据连接状态自动禁用
5. **合规性**：遵守应用商店的订阅政策

## 📚 相关文档

- [Expo IAP 官方文档](https://expo-iap.hyo.dev/)
- [Apple App Store 订阅指南](https://developer.apple.com/app-store/subscriptions/)
- [Google Play 订阅指南](https://developer.android.com/google/play/billing/subscriptions)
