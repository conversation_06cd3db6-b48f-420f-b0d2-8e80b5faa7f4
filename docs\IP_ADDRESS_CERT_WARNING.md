# IP地址证书固定警告功能

## 概述

由于 TrustKit 库的限制，SSL证书固定功能无法用于IP地址，只能用于域名。为了提高用户体验，我们在配置表单中添加了IP地址检测和警告功能。

## 功能描述

当用户在配置表单中：
1. 选择 HTTPS 协议
2. 输入IP地址作为服务器地址
3. 填写了公钥哈希（证书固定）

系统会自动检测到这种情况并显示警告信息："IP地址无法用于SSL证书固定，请使用域名。"

## 实现细节

### 文件修改

1. **配置表单文件**：
   - `app/config-form/3x-ui.tsx`
   - `app/config-form/x-ui.tsx`
   - `app/config-form/s-ui.tsx`

2. **翻译文件**：
   - `lib/i18n.ts` - 添加了多语言支持

3. **测试文件**：
   - `__tests__/ip-address-validation.test.ts` - IP地址检测功能测试

### IP地址检测逻辑

```typescript
const isIPAddress = (hostname: string): boolean => {
  // IPv4 正则表达式（更严格，不允许前导零）
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])$/;
  // IPv6 正则表达式（简化版）
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
  
  return ipv4Regex.test(hostname) || ipv6Regex.test(hostname);
};
```

### 验证触发时机

警告会在以下情况下触发：
1. **表单验证时**：用户点击保存按钮时
2. **实时验证时**：用户输入URL或证书内容时
3. **协议切换时**：用户从HTTP切换到HTTPS时

### 多语言支持

- **英文**：`IP addresses cannot be used for SSL certificate pinning. Please use a domain name instead.`
- **简体中文**：`IP地址无法用于SSL证书固定，请使用域名。`
- **繁体中文**：`IP位址無法用於SSL憑證固定，請使用網域名稱。`
- **波斯语**：`آدرس IP نمی‌تواند برای پین کردن گواهی SSL استفاده شود. لطفاً از نام دامنه استفاده کنید.`

## 测试覆盖

测试文件包含以下测试用例：
- IPv4地址检测（有效和无效）
- IPv6地址检测（有效和无效）
- 域名检测（确保不被误识别为IP）
- 边界情况处理（空字符串、特殊字符等）

## 技术限制

1. IPv6地址检测使用简化版正则表达式，可能不覆盖所有IPv6格式
2. 不支持带端口号的IP地址检测（如 `***********:8080`）
3. 不支持IPv4映射的IPv6地址等特殊格式

## 用户体验

- 警告信息显示在证书字段下方，使用红色文本
- 不会阻止用户保存配置，只是提供警告信息
- 实时验证提供即时反馈，无需等到提交表单
