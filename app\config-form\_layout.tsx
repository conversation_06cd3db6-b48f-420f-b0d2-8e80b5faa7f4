import { Stack } from 'expo-router';
import React from 'react';
import { Dimensions, Platform,View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function Layout() {
  const height = Dimensions.get('screen').height;
  const { bottom } = useSafeAreaInsets();
  const bottomValue = bottom > 0 ? bottom : 16;

  const containerStyle =
    Platform.OS === 'ios'
      ? { height: height - bottomValue * 2 }
      : { flex: 1 };

  return (
    <View style={containerStyle}>
      <Stack></Stack>
    </View>
  );
}