import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import SettingsScreen from '@/app/(tabs)/settings';

// Mock dependencies
jest.mock('@/hooks/useThemeColor', () => ({
  useThemeColor: () => '#ffffff',
}));

jest.mock('@/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock('@/lib/store', () => ({
  useAppStore: () => ({
    settings: {
      theme: 'system',
      language: 'en',
      isPro: false,
    },
    updateSettings: jest.fn(),
    configs: [],
  }),
}));

jest.mock('@/hooks/useSubscription', () => ({
  useSubscription: () => ({
    isPro: false,
    isLoading: false,
    purchasePro: jest.fn().mockResolvedValue(undefined),
    restorePurchases: jest.fn().mockResolvedValue(undefined),
    error: null,
  }),
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('SettingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = render(<SettingsScreen />);
    
    expect(getByText('settings.title')).toBeTruthy();
    expect(getByText('settings.proPlan')).toBeTruthy();
    expect(getByText('settings.theme')).toBeTruthy();
    expect(getByText('settings.language')).toBeTruthy();
    expect(getByText('settings.messengerGroup')).toBeTruthy();
  });

  it('shows Pro subscription section', () => {
    const { getByText } = render(<SettingsScreen />);
    
    expect(getByText('settings.subscribeToPro')).toBeTruthy();
    expect(getByText('settings.restorePurchases')).toBeTruthy();
    expect(getByText('settings.proDescription')).toBeTruthy();
  });

  it('handles theme selection', async () => {
    const { getByText } = render(<SettingsScreen />);
    
    const themeButton = getByText('settings.theme');
    fireEvent.press(themeButton);
    
    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalled();
    });
  });

  it('handles language selection', async () => {
    const { getByText } = render(<SettingsScreen />);
    
    const languageButton = getByText('settings.language');
    fireEvent.press(languageButton);
    
    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalled();
    });
  });

  it('handles Pro purchase', async () => {
    const { getByText } = render(<SettingsScreen />);
    
    const purchaseButton = getByText('settings.subscribeToPro');
    fireEvent.press(purchaseButton);
    
    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'common.success',
        'settings.restoreSuccess'
      );
    });
  });

  it('handles restore purchases', async () => {
    const { getByText } = render(<SettingsScreen />);
    
    const restoreButton = getByText('settings.restorePurchases');
    fireEvent.press(restoreButton);
    
    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'common.success',
        'settings.restoreSuccess'
      );
    });
  });
});
