/**
 * IP地址检测功能测试
 */

// 从配置表单中提取的IP地址检测函数
const isIPAddress = (hostname: string): boolean => {
  // IPv4 正则表达式（更严格，不允许前导零）
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])$/;
  // IPv6 正则表达式（简化版）
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

  return ipv4Regex.test(hostname) || ipv6Regex.test(hostname);
};

describe('IP地址检测功能', () => {
  describe('IPv4地址检测', () => {
    test('应该正确识别有效的IPv4地址', () => {
      expect(isIPAddress('***********')).toBe(true);
      expect(isIPAddress('********')).toBe(true);
      expect(isIPAddress('**********')).toBe(true);
      expect(isIPAddress('127.0.0.1')).toBe(true);
      expect(isIPAddress('***************')).toBe(true);
      expect(isIPAddress('0.0.0.0')).toBe(true);
    });

    test('应该正确拒绝无效的IPv4地址', () => {
      expect(isIPAddress('256.1.1.1')).toBe(false);
      expect(isIPAddress('192.168.1')).toBe(false);
      expect(isIPAddress('***********.1')).toBe(false);
      expect(isIPAddress('************')).toBe(false); // 前导零
      expect(isIPAddress('192.168.-1.1')).toBe(false);
    });
  });

  describe('IPv6地址检测', () => {
    test('应该正确识别有效的IPv6地址', () => {
      expect(isIPAddress('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true);
      expect(isIPAddress('::1')).toBe(true);
      expect(isIPAddress('::')).toBe(true);
    });

    test('应该正确拒绝无效的IPv6地址', () => {
      expect(isIPAddress('2001:0db8:85a3::8a2e:0370:7334:extra')).toBe(false);
      expect(isIPAddress('2001:0db8:85a3')).toBe(false);
      expect(isIPAddress('gggg::1')).toBe(false);
    });
  });

  describe('域名检测', () => {
    test('应该正确拒绝域名', () => {
      expect(isIPAddress('example.com')).toBe(false);
      expect(isIPAddress('sub.example.com')).toBe(false);
      expect(isIPAddress('localhost')).toBe(false);
      expect(isIPAddress('my-server.local')).toBe(false);
      expect(isIPAddress('***********.example.com')).toBe(false);
    });
  });

  describe('边界情况', () => {
    test('应该正确处理空字符串和特殊字符', () => {
      expect(isIPAddress('')).toBe(false);
      expect(isIPAddress(' ')).toBe(false);
      expect(isIPAddress('*********** ')).toBe(false); // 带空格
      expect(isIPAddress(' ***********')).toBe(false); // 带空格
      expect(isIPAddress('***********:8080')).toBe(false); // 带端口
    });
  });
});
