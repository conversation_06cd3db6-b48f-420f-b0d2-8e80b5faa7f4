import { router, Stack } from 'expo-router';
import React from 'react';
import { Dimensions, Platform,View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Label } from '~/components/ui/label';
import { useThemeColor } from '~/hooks/useThemeColor';
import { useTranslation } from '~/hooks/useTranslation';

export default function Layout() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  const handleCloseModal = () => {
    router.back();
  };
  const height = Dimensions.get('screen').height;
  const { bottom } = useSafeAreaInsets();
  const bottomValue = bottom > 0 ? bottom : 16;

  const containerStyle =
    Platform.OS === 'ios'
      ? { height: height - bottomValue * 2 }
      : { flex: 1 };

  return (
    <View style={containerStyle}>
      <Stack screenOptions={{headerShown: true,
                headerLeft: () => (
                  <Label onPress={handleCloseModal} style={{ color: textColor }}>
                    {t('common.cancel')}
                  </Label>
                ),}}></Stack>
    </View>
  );
}