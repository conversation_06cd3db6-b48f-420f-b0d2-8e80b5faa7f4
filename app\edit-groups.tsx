import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal, SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Text } from '@/components/ui/text';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { getGroupDisplayName, isGroupEditable } from '@/lib/groupUtils';
import { useAppStore } from '@/lib/store';
import { Group } from '@/lib/types';
import { ChevronDown, ChevronUp, Edit3, Plus, Trash2 } from 'lucide-react-native';

export default function EditGroupsScreen() {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const backgroundColor = useThemeColor({}, 'background');

  // 根据主题设置颜色
  const cardBackgroundColor = colorScheme === 'dark' ? '#1C1C1E' : '#f9f9f9';
  const borderColor = useThemeColor({}, 'border');
  const iconColor = colorScheme === 'dark' ? '#ffffff' : '#000000';
  const destructiveColor = colorScheme === 'dark' ? '#ff6b6b' : '#dc2626';

  const { groups, addGroup, updateGroup, deleteGroup, reorderGroups } = useAppStore();
  const [editableGroups, setEditableGroups] = useState<Group[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [groupName, setGroupName] = useState('');

  // 显示所有分组，包括 'default' 分组（全部分组）
  useEffect(() => {
    // 按 order 排序
    const sortedGroups = [...groups].sort((a, b) => a.order - b.order);
    setEditableGroups(sortedGroups);
  }, [groups]);

  const moveGroup = async (index: number, direction: 'up' | 'down') => {
    const newGroups = [...editableGroups];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;

    if (targetIndex < 0 || targetIndex >= newGroups.length) return;

    // 交换位置
    [newGroups[index], newGroups[targetIndex]] = [newGroups[targetIndex], newGroups[index]];

    // 更新 order
    newGroups.forEach((group, idx) => {
      group.order = idx;
    });

    setEditableGroups(newGroups);

    // 即时持久化分组顺序
    await reorderGroups(newGroups);
  };

  const handleStartEdit = (group: Group) => {
    if (!isGroupEditable(group.id)) {
      Alert.alert(t('common.error'), t('groups.cannotRenameDefault'));
      return;
    }
    setIsEditMode(true);
    setEditingGroup(group);
    setGroupName(group.name);
    setModalVisible(true);
  };

  const handleStartAdd = () => {
    setIsEditMode(false);
    setEditingGroup(null);
    setGroupName('');
    setModalVisible(true);
  };

  const handleSave = async () => {
    if (!groupName.trim()) return;

    if (isEditMode && editingGroup) {
      await updateGroup(editingGroup.id, { name: groupName.trim() });
    } else {
      await addGroup(groupName.trim());
    }

    setModalVisible(false);
    setEditingGroup(null);
    setGroupName('');
  };

  const handleCancel = () => {
    setModalVisible(false);
    setEditingGroup(null);
    setGroupName('');
  };

  const handleDelete = (group: Group) => {
    if (!isGroupEditable(group.id)) {
      Alert.alert(t('common.error'), t('groups.cannotDeleteDefault'));
      return;
    }

    Alert.alert(
      t('groups.deleteGroup'),
      t('groups.deleteConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { 
          text: t('common.delete'), 
          style: 'destructive',
          onPress: () => deleteGroup(group.id)
        },
      ]
    );
  };





  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 分组列表 */}
      <ScrollView style={styles.content}>
        {editableGroups.map((group, index) => (
          <View key={group.id} style={[styles.groupItem, { backgroundColor: cardBackgroundColor, borderColor }]}>
            <View style={styles.groupInfo}>
              <Text className="text-lg font-medium">
                {getGroupDisplayName(group, t)}
              </Text>
            </View>

            <View style={styles.groupActions}>
                {/* 排序按钮 */}
                <View style={styles.sortButtons}>
                  <Button
                    variant="ghost"
                    size="sm"
                    onPress={() => moveGroup(index, 'up')}
                    disabled={index === 0}
                    style={styles.actionButton}
                  >
                    <ChevronUp size={16} color={iconColor} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onPress={() => moveGroup(index, 'down')}
                    disabled={index === editableGroups.length - 1}
                    style={styles.actionButton}
                  >
                    <ChevronDown size={16} color={iconColor} />
                  </Button>
                </View>

                {/* 编辑和删除按钮 */}
                {isGroupEditable(group.id) && (
                  <View style={styles.editDeleteButtons}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onPress={() => handleStartEdit(group)}
                        style={styles.actionButton}
                      >
                        <Edit3 size={16} color={iconColor} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onPress={() => handleDelete(group)}
                        style={styles.actionButton}
                      >
                        <Trash2 size={16} color={destructiveColor} />
                      </Button>
                  </View>
                )}
              </View>
          </View>
        ))}

        {/* 添加新分组按钮 */}
        <Button
          variant="secondary"
          onPress={handleStartAdd}
          style={styles.addButton}
        >
          <Plus size={16} color={iconColor} />
          <Text style={styles.addButtonText}>{t('groups.addGroup')}</Text>
        </Button>
      </ScrollView>

      {/* 分组模态框 */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancel}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: cardBackgroundColor, borderColor }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: iconColor }]}>
                {isEditMode ? t('groups.renameGroup') : t('groups.addGroup')}
              </Text>
            </View>

            <View style={styles.modalBody}>
              <Input
                value={groupName}
                onChangeText={setGroupName}
                placeholder={isEditMode ? t('groups.newGroupName') : t('groups.enterGroupName')}
                style={styles.modalInput}
                autoFocus
              />
            </View>

            <View style={styles.modalFooter}>
              <Button
                variant="ghost"
                onPress={handleCancel}
                style={styles.modalButton}
              >
                <Text>{t('common.cancel')}</Text>
              </Button>
              <Button
                variant="default"
                onPress={handleSave}
                style={styles.modalButton}
              >
                <Text>{isEditMode ? t('common.save') : t('common.add')}</Text>
              </Button>
            </View>
          </View>
        </View>
      </Modal>

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  content: {
    flex: 1,
    padding: 16,
    paddingBottom: 16, // 移除底部按钮后调整底部间距
  },
  groupItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    // backgroundColor and borderColor will be set dynamically
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  groupInfo: {
    flex: 1,
  },
  editContainer: {
    flex: 1,
  },
  editInput: {
    marginBottom: 8,
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  groupActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sortButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  editDeleteButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  actionButton: {
    minWidth: 32,
    minHeight: 32,
  },

  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
  },
  addButtonText: {
    fontSize: 16,
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 12,
    borderWidth: 1,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  modalBody: {
    padding: 20,
  },
  modalInput: {
    marginBottom: 0,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalButton: {
    minWidth: 80,
  },
});
