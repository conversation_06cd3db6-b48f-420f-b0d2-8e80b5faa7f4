import { Drawer } from 'expo-router/drawer';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { Home, List, Route, ArrowLeftRight, Menu,Undo2 } from 'lucide-react-native';
import React from 'react';
import { DrawerContentScrollView, DrawerItemList, DrawerItem } from '@react-navigation/drawer';
import { View, StyleSheet } from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { useAppStore } from '~/lib/store';

// 自定义 Drawer Content 组件
function CustomDrawerContent(props: any) {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const handleBackToHome = () => {
    // 使用 router.navigate 返回主页
    router.replace("/")
  };

  return (
    <View style={[styles.drawerContainer, { backgroundColor }]}>
      <DrawerContentScrollView {...props} style={styles.scrollView}>
        <DrawerItemList {...props} />
      </DrawerContentScrollView>

      {/* Footer 区域 */}
      <View style={[styles.footer, { borderTopColor: borderColor }]}>
        <DrawerItem
          label={t('threeXUI.drawer.backToHome')}
          onPress={handleBackToHome}
          pressOpacity={0.5}
          icon={({ color, size }) => <Undo2 color={color} size={size} />}
          labelStyle={{ color: textColor, fontSize: 16, fontWeight: '500' }}
          activeTintColor={textColor}
          inactiveTintColor={textColor + '80'}
        />
      </View>
    </View>
  );
}

export default function ThreeXUILayout() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { configs } = useAppStore()

  return (
    <>
      <Drawer
        drawerContent={(props) => <CustomDrawerContent {...props} />}
        screenOptions={{
          drawerStyle: {
            backgroundColor,
            borderRightColor: borderColor,
            borderRightWidth: 1,
          },
          drawerLabelStyle: {
            color: textColor,
            fontSize: 16,
            fontWeight: '500',
          },
          drawerActiveTintColor: textColor,
          drawerInactiveTintColor: textColor + '80',
          headerTintColor: textColor,
          headerTitleStyle: {
            color: textColor,
            fontSize: 18,
            fontWeight: '600',
          },
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: t('threeXUI.drawer.overview'),
            title: configs.find(c => c.id === configId)?.name,
            drawerIcon: ({ color, size }) => (
              <Home color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
        <Drawer.Screen
          name="inbounds"
          options={{
            drawerLabel: t('threeXUI.drawer.inbounds'),
            title: t('threeXUI.inbounds.title'),
            drawerIcon: ({ color, size }) => (
              <List color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
        <Drawer.Screen
          name="routing"
          options={{
            drawerLabel: t('threeXUI.drawer.routing'),
            title: t('threeXUI.routing.title'),
            drawerIcon: ({ color, size }) => (
              <Route color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
        <Drawer.Screen
          name="outbounds"
          options={{
            drawerLabel: t('threeXUI.drawer.outbounds'),
            title: t('threeXUI.drawer.outbounds'),
            drawerIcon: ({ color, size }) => (
              <ArrowLeftRight color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
      </Drawer>
    </>
  );
}

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  footer: {
    borderTopWidth: 1,
    paddingTop: 8,
    paddingBottom: 24,
  },
});
