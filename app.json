{"expo": {"name": "RayBoxUI", "slug": "native-ui", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": false, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}, "bundleIdentifier": "com.kathleen.rayboxui"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-web-browser", "expo-secure-store", "expo-localization", "expo-iap", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-build-properties", {"ios": {"networkInspector": false, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}}, "android": {"kotlinVersion": "2.0.21"}}], ["expo-splash-screen", {"backgroundColor": "#ffffff", "image": "./assets/images/light.png", "dark": {"image": "./assets/images/dark.png", "backgroundColor": "#000000"}, "imageWidth": 200}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "75cef86e-0c74-4db6-9d21-ce65c72566ba"}}, "owner": "<PERSON><PERSON><PERSON><PERSON>"}}