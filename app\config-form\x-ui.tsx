import { router, useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert as RNAlert, SafeAreaView, StyleSheet, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SelectionGroup, SelectionOption } from '~/components/selection-group';
import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { getGroupDisplayName } from '@/lib/groupUtils';
import { useAppStore } from '@/lib/store';
import { ConfigFormData, ProtocolType, XUIConfig } from '@/lib/types';

import { Alert<PERSON>riangle, HelpCircle } from 'lucide-react-native';
import { validateXUIConnection } from '~/panels/x-ui/utils';

export default function XUIConfigFormScreen() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const { addConfig, updateConfig, groups, configs } = useAppStore();
  const { initialData } = useLocalSearchParams<{ initialData: string }>();
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const navigation = useNavigation();

  const isEditMode = !!configId;

  // 设置动态header标题
  useEffect(() => {
    navigation.setOptions({
      title: isEditMode ? t('configForm.editXUIConfig') : t('configForm.addXUIConfig'),
    });
  }, [navigation, isEditMode, t]);

  // 过滤掉"全部"分组
  const availableGroups = groups.filter(group => group.id !== 'all');

  // 分组选项
  const groupOptions: SelectionOption[] = availableGroups.map(group => ({
    id: group.id,
    label: getGroupDisplayName(group, t),
  }));

  // 获取初始数据：优先使用configId获取，其次使用initialData参数
  let initialDataParsed = null;
  if (configId) {
    // 通过configId获取配置数据
    const existingConfig = configs.find(config => config.id === configId && config.type === 'x-ui');
    if (existingConfig) {
      const xuiConfig = existingConfig as XUIConfig;
      // 从配置的分组数组中排除'all'分组，因为它在UI中不显示
      const selectedGroupIds = xuiConfig.groupIds.filter(groupId => groupId !== 'all');

      initialDataParsed = {
        name: xuiConfig.name,
        url: xuiConfig.url,
        protocol: xuiConfig.protocol,
        groupId: selectedGroupIds, // 包含所有选中的分组（除了'all'）
        username: xuiConfig.username,
        password: xuiConfig.password,
        cert: xuiConfig.cert || '',
      };
    }
  } else if (initialData) {
    // 使用传入的initialData参数
    initialDataParsed = JSON.parse(initialData);
  }

  const [formData, setFormData] = useState<ConfigFormData>({
    name: initialDataParsed?.name || '',
    url: initialDataParsed?.url || '',
    protocol: initialDataParsed?.protocol || 'https',
    groupId: initialDataParsed?.groupId || [],
    username: initialDataParsed?.username || '',
    password: initialDataParsed?.password || '',
    cert: initialDataParsed?.cert || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 检测是否为IP地址格式
  const isIPAddress = (hostname: string): boolean => {
    // IPv4 正则表达式（更严格，不允许前导零）
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])$/;
    // IPv6 正则表达式（简化版）
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

    return ipv4Regex.test(hostname) || ipv6Regex.test(hostname);
  };

  const handleProtocolChange = (protocol: ProtocolType) => {
    setFormData(prev => ({ ...prev, protocol }));
    // 清除证书字段的错误
    if (errors.cert) {
      setErrors(prev => ({ ...prev, cert: '' }));
    }

    // 如果切换到HTTPS且有证书内容，检查IP地址警告
    if (protocol === 'https' && formData.cert && formData.cert.trim()) {
      setTimeout(() => {
        try {
          const fullUrl = new URL(protocol + '://' + formData.url);
          const hostname = fullUrl.hostname;
          if (isIPAddress(hostname)) {
            setErrors(prev => ({ ...prev, cert: t('configForm.ipAddressCertWarning') }));
          }
        } catch {
          // URL格式错误时不显示IP警告
        }
      }, 0);
    }
  };

  const handleInputChange = (field: keyof ConfigFormData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // 如果是证书字段或URL字段变化，需要重新检查IP地址警告
    if (field === 'cert' || field === 'url') {
      setTimeout(() => {
        // 延迟执行验证，确保状态已更新
        const updatedFormData = { ...formData, [field]: value };
        if (updatedFormData.protocol === 'https' && updatedFormData.cert && typeof updatedFormData.cert === 'string' && updatedFormData.cert.trim()) {
          try {
            const fullUrl = new URL(updatedFormData.protocol + '://' + updatedFormData.url);
            const hostname = fullUrl.hostname;
            if (isIPAddress(hostname)) {
              setErrors(prev => ({ ...prev, cert: t('configForm.ipAddressCertWarning') }));
            }
          } catch {
            // URL格式错误时不显示IP警告
          }
        }
      }, 0);
    }
  };

  const showCertAlert = () => {
    RNAlert.alert(
      t('configForm.publicKeyHash'),
      t('configForm.publicKeyHashTooltip'),
      [{ text: t('common.ok'), style: 'default' }]
    );
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('validation.required');
    }

    if (!formData.url.trim()) {
      newErrors.url = t('validation.required');
    } else {
      // 简单的URL验证
      try {
        const fullUrl = new URL(formData.protocol + '://' + formData.url);

        // 检查是否为IP地址且使用了HTTPS协议和证书固定
        if (formData.protocol === 'https' && formData.cert && formData.cert.trim()) {
          const hostname = fullUrl.hostname;
          if (isIPAddress(hostname)) {
            newErrors.cert = t('configForm.ipAddressCertWarning');
          }
        }
      } catch {
        newErrors.url = t('validation.invalidUrl');
      }
    }

    if (!formData.username?.trim()) {
      newErrors.username = t('validation.required');
    }

    if (!formData.password?.trim()) {
      newErrors.password = t('validation.required');
    }

    // 分组验证已移除，因为配置会自动添加到"全部"分组

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 处理URL，删除末尾的斜杠
      const cleanUrl = formData.url.replace(/\/+$/, '');

      // 处理公钥哈希（如果有）- 直接使用十六进制格式，在initializePinning中进行base64转换
      let publicKeyHashes: string[] = [];
      if (formData.cert && formData.cert.trim()) {
        publicKeyHashes = formData.cert.split('\n').map(line => line.trim()).filter(line => line.length > 0);
      }

      // 创建用于验证的表单数据，使用清理后的URL
      const validationFormData = {
        ...formData,
        url: cleanUrl,
      };

      // 验证API连接
      const validationResult = await validateXUIConnection(validationFormData, publicKeyHashes);

      if (!validationResult.success) {
        RNAlert.alert(
          t('common.error'),
          validationResult.error || t('configForm.connectionFailed'),
          [{ text: t('common.ok'), style: 'default' }]
        );
        return;
      }

      // 保留所有选中的分组
      const groupIds = Array.isArray(formData.groupId) ? formData.groupId : [formData.groupId].filter(Boolean);

      const submitData = {
        ...formData,
        url: cleanUrl, // 使用清理后的URL
        groupId: groupIds, // 保留所有选中的分组
        certFingerprints: publicKeyHashes, // 添加处理后的公钥哈希
      };

      // 处理表单提交
      if (isEditMode && configId) {
        // 编辑模式：使用updateConfig方法处理分组变更
        const selectedGroupIds = Array.isArray(submitData.groupId) ? submitData.groupId : [submitData.groupId].filter(Boolean);

        await updateConfig(configId, {
          name: submitData.name,
          url: submitData.url,
          protocol: submitData.protocol,
          cert: submitData.cert,
          certFingerprints: submitData.certFingerprints || [],
          username: submitData.username!,
          password: submitData.password!,
        }, selectedGroupIds);
      } else {
        // 新增模式：使用addConfig方法
        await addConfig(submitData, 'x-ui');
      }

      // 关闭模态框并返回主页
      router.back();
    } catch (error) {
      console.error('Failed to submit X-UI config:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      RNAlert.alert(
        t('common.error'),
        errorMessage,
        [{ text: t('common.ok'), style: 'default' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <KeyboardAwareScrollView style={styles.scrollView} bottomOffset={50}>
          <View style={styles.form}>
          {/* 配置名称 */}
          <View style={styles.field}>
            <Label>{t('configForm.name')}</Label>
            <Input
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder={t('configForm.namePlaceholder')}
              style={errors.name ? styles.errorInput : undefined}
            />
            {errors.name && (
              <Text style={styles.errorText}>{errors.name}</Text>
            )}
          </View>
          <View style={styles.field}>
            <Label>{t('configForm.protocol')}</Label>
            <View style={styles.protocolButtons}>
              <Button
                variant={formData.protocol === 'https' ? 'default' : 'secondary'}
                size="sm"
                onPress={() => handleProtocolChange('https')}
                style={styles.protocolButton}
              >
                <Text>HTTPS</Text>
              </Button>
              <Button
                variant={formData.protocol === 'http' ? 'default' : 'secondary'}
                size="sm"
                onPress={() => handleProtocolChange('http')}
                style={styles.protocolButton}
              >
                <Text>HTTP</Text>
              </Button>
            </View>
          </View>

          {/* HTTP 安全警告 */}
          {formData.protocol === 'http' && (
            <Alert variant="destructive" icon={AlertTriangle} style={styles.alert}>
              <AlertTitle>{t('configForm.httpWarningTitle')}</AlertTitle>
              <AlertDescription>
                {t('configForm.httpWarning')}
              </AlertDescription>
            </Alert>
          )}

          {/* URL */}
          <View style={styles.field}>
            <Label>{t('configForm.url')}</Label>
            <Input
              value={formData.url}
              onChangeText={(value) => handleInputChange('url', value)}
              placeholder="example.com:54321"
              style={errors.url ? styles.errorInput : undefined}
            />
            {errors.url && (
              <Text style={styles.errorText}>{errors.url}</Text>
            )}
          </View>

          {/* 用户名 */}
          <View style={styles.field}>
            <Label>{t('configForm.username')}</Label>
            <Input
              value={formData.username}
              onChangeText={(value) => handleInputChange('username', value)}
              placeholder={t('configForm.usernamePlaceholder')}
              style={errors.username ? styles.errorInput : undefined}
            />
            {errors.username && (
              <Text style={styles.errorText}>{errors.username}</Text>
            )}
          </View>

          {/* 密码 */}
          <View style={styles.field}>
            <Label>{t('configForm.password')}</Label>
            <Input
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              placeholder={t('configForm.passwordPlaceholder')}
              secureTextEntry
              style={errors.password ? styles.errorInput : undefined}
            />
            {errors.password && (
              <Text style={styles.errorText}>{errors.password}</Text>
            )}
          </View>

          {/* HTTPS 公钥SHA256 */}
          {formData.protocol === 'https' && (
            <View style={styles.field}>
              <View style={styles.labelWithTooltip}>
                <Label>{t('configForm.publicKeyHash')}</Label>
                <HelpCircle onPress={showCertAlert} size={16} color="#666" />
              </View>
              <Input
                value={formData.cert}
                onChangeText={(value) => handleInputChange('cert', value)}
                placeholder="a1b2c3d4e5f6...def123456b2c3&#10;9876543210fe...ba0987654321"
                multiline
                style={styles.multilineInput}
              />
            </View>
          )}

          {/* 分组选择 */}
          <SelectionGroup
            label={t('configForm.group')}
            options={groupOptions}
            value={formData.groupId}
            onChange={(value) => handleInputChange('groupId', value)}
            multiple={true}
            horizontal={true}
            buttonSize="sm"
            error={errors.groupId}
          />

          </View>
        </KeyboardAwareScrollView>

        {/* 固定在底部的提交按钮 */}
        <View style={[styles.bottomActions, { borderTopColor: borderColor, backgroundColor }]}>
          <Button
            variant="default"
            onPress={handleSubmit}
            style={styles.submitButton}
            disabled={isSubmitting}
          >
            <Text>{isSubmitting ? t('common.connecting') : t('common.save')}</Text>
          </Button>
        </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  form: {
    padding: 16,
    gap: 16,
    paddingBottom: 100, // 为底部按钮留出空间
  },
  field: {
    gap: 8,
  },
  protocolButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  protocolButton: {
    flex: 1,
  },
  alert: {
    marginVertical: 8,
  },
  alertText: {
    fontSize: 14,
  },
  labelWithTooltip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },

  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    // backgroundColor and borderTopColor will be set dynamically
    borderTopWidth: 1,
  },
  submitButton: {
    width: '100%',
  },
  errorInput: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
  },
  hintText: {
    color: '#666',
    fontSize: 12,
    marginBottom: 8,
  },
  multilineInput: {
    height: 90,
  },
});
